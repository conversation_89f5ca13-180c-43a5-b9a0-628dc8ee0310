<template>
  <div class="user-center">
    <!-- 头部背景区域 -->
    <div class="header-bg">
      <div class="user-info">
        <div class="avatar-section">
          <el-avatar :size="120" :src="userInfo.avatar" class="avatar">
            <img src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png" />
          </el-avatar>
          <el-upload
            class="avatar-uploader"
            action="/api/upload/avatar"
            :show-file-list="false"
            :on-success="handleAvatarSuccess"
            :before-upload="beforeAvatarUpload"
          >
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-camera"
              circle
              class="upload-btn"
            ></el-button>
          </el-upload>
          <div class="level-badge">
            <el-tag type="warning" size="small">Lv.{{ userInfo.level }}</el-tag>
          </div>
        </div>
        <div class="info">
          <h2>{{ userInfo.username }}</h2>
          <p class="email">{{ userInfo.email }}</p>
          <div class="status-tags">
            <el-tag :type="userInfo.status === 'active' ? 'success' : 'info'" size="small">
              {{ userInfo.status === 'active' ? '在线' : '离线' }}
            </el-tag>
            <el-tag v-if="userInfo.vip" type="warning" size="small">VIP</el-tag>
            <el-tag v-if="userInfo.verified" type="success" size="small">已认证</el-tag>
          </div>
          <div class="stats">
            <div class="stat-item">
              <span class="stat-number">{{ userInfo.posts }}</span>
              <span class="stat-label">动态</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">{{ userInfo.followers }}</span>
              <span class="stat-label">粉丝</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">{{ userInfo.following }}</span>
              <span class="stat-label">关注</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">{{ userInfo.visits }}</span>
              <span class="stat-label">访问</span>
            </div>
          </div>
        </div>
        <div class="actions">
          <el-button type="primary" @click="editProfile">
            <i class="el-icon-edit"></i> 编辑资料
          </el-button>
          <el-button @click="showQRCode"> <i class="el-icon-share"></i> 分享名片 </el-button>
        </div>
      </div>
    </div>

    <!-- 快捷操作栏 -->
    <div class="quick-actions">
      <div class="container">
        <el-card shadow="hover" class="quick-card">
          <div class="quick-item" @click="handleQuickAction('message')">
            <i class="el-icon-message"></i>
            <span>消息中心</span>
            <el-badge v-if="unreadMessages > 0" :value="unreadMessages"></el-badge>
          </div>
        </el-card>
        <el-card shadow="hover" class="quick-card">
          <div class="quick-item" @click="handleQuickAction('wallet')">
            <i class="el-icon-wallet"></i>
            <span>我的钱包</span>
            <span class="balance">¥{{ userInfo.balance }}</span>
          </div>
        </el-card>
        <el-card shadow="hover" class="quick-card">
          <div class="quick-item" @click="handleQuickAction('orders')">
            <i class="el-icon-document"></i>
            <span>我的订单</span>
          </div>
        </el-card>
        <el-card shadow="hover" class="quick-card">
          <div class="quick-item" @click="handleQuickAction('favorites')">
            <i class="el-icon-star-on"></i>
            <span>我的收藏</span>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="content">
      <el-row :gutter="20">
        <!-- 左侧菜单 -->
        <el-col :span="6">
          <el-card shadow="never" class="menu-card">
            <div class="menu-section">
              <h4>个人资料</h4>
              <el-menu :default-active="activeTab" class="user-menu" @select="handleMenuSelect">
                <el-menu-item index="basicInfo">
                  <i class="el-icon-user"></i>
                  <span>基本信息</span>
                </el-menu-item>
                <el-menu-item index="skills">
                  <i class="el-icon-trophy"></i>
                  <span>技能认证</span>
                </el-menu-item>
                <el-menu-item index="activity">
                  <i class="el-icon-data-line"></i>
                  <span>活动记录</span>
                </el-menu-item>
              </el-menu>
            </div>

            <div class="menu-section">
              <h4>安全中心</h4>
              <el-menu :default-active="activeTab" class="user-menu" @select="handleMenuSelect">
                <el-menu-item index="security">
                  <i class="el-icon-lock"></i>
                  <span>安全设置</span>
                </el-menu-item>
                <el-menu-item index="devices">
                  <i class="el-icon-mobile-phone"></i>
                  <span>设备管理</span>
                </el-menu-item>
                <el-menu-item index="loginLog">
                  <i class="el-icon-time"></i>
                  <span>登录记录</span>
                </el-menu-item>
              </el-menu>
            </div>

            <div class="menu-section">
              <h4>偏好设置</h4>
              <el-menu :default-active="activeTab" class="user-menu" @select="handleMenuSelect">
                <el-menu-item index="notification">
                  <i class="el-icon-message"></i>
                  <span>消息通知</span>
                </el-menu-item>
                <el-menu-item index="privacy">
                  <i class="el-icon-view"></i>
                  <span>隐私设置</span>
                </el-menu-item>
                <el-menu-item index="theme">
                  <i class="el-icon-brush"></i>
                  <span>主题设置</span>
                </el-menu-item>
              </el-menu>
            </div>

            <div class="menu-section">
              <h4>账户管理</h4>
              <el-menu :default-active="activeTab" class="user-menu" @select="handleMenuSelect">
                <el-menu-item index="wallet">
                  <i class="el-icon-wallet"></i>
                  <span>钱包管理</span>
                </el-menu-item>
                <el-menu-item index="backup">
                  <i class="el-icon-download"></i>
                  <span>数据备份</span>
                </el-menu-item>
                <el-menu-item index="account">
                  <i class="el-icon-setting"></i>
                  <span>账户设置</span>
                </el-menu-item>
              </el-menu>
            </div>
          </el-card>
        </el-col>

        <!-- 右侧内容 -->
        <el-col :span="18">
          <!-- 基本信息 -->
          <el-card v-if="activeTab === 'basicInfo'" shadow="never" class="content-card">
            <div slot="header" class="card-header">
              <span>基本信息</span>
              <div class="header-actions">
                <el-button v-if="!editMode" type="text" @click="editProfile">
                  <i class="el-icon-edit"></i> 编辑
                </el-button>
              </div>
            </div>
            <el-form ref="userForm" :model="userInfo" :rules="rules" label-width="120px">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="用户名" prop="username">
                    <el-input v-model="userInfo.username" :disabled="!editMode">
                      <template slot="prefix">
                        <i class="el-icon-user"></i>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="昵称" prop="nickname">
                    <el-input v-model="userInfo.nickname" :disabled="!editMode">
                      <template slot="prefix">
                        <i class="el-icon-star-off"></i>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="邮箱" prop="email">
                    <el-input v-model="userInfo.email" :disabled="!editMode">
                      <template slot="prefix">
                        <i class="el-icon-message"></i>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="手机号码" prop="phone">
                    <el-input v-model="userInfo.phone" :disabled="!editMode">
                      <template slot="prefix">
                        <i class="el-icon-phone"></i>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="性别">
                    <el-select v-model="userInfo.gender" :disabled="!editMode" style="width: 100%">
                      <el-option label="男" value="male"></el-option>
                      <el-option label="女" value="female"></el-option>
                      <el-option label="保密" value="secret"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="生日">
                    <el-date-picker
                      v-model="userInfo.birthday"
                      type="date"
                      placeholder="选择日期"
                      :disabled="!editMode"
                      style="width: 100%"
                    >
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="所在城市">
                    <el-cascader
                      v-model="userInfo.location"
                      :options="cityOptions"
                      :disabled="!editMode"
                      placeholder="选择城市"
                      style="width: 100%"
                    ></el-cascader>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="职业">
                    <el-input v-model="userInfo.occupation" :disabled="!editMode">
                      <template slot="prefix">
                        <i class="el-icon-suitcase"></i>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="公司">
                    <el-input v-model="userInfo.company" :disabled="!editMode">
                      <template slot="prefix">
                        <i class="el-icon-office-building"></i>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item label="兴趣标签">
                <template v-if="!editMode">
                  <el-tag
                    v-for="tag in userInfo.tags"
                    :key="tag"
                    style="margin-right: 8px; margin-bottom: 8px"
                  >
                    {{ tag }}
                  </el-tag>
                </template>
                <template v-else>
                  <el-tag
                    v-for="tag in userInfo.tags"
                    :key="tag"
                    closable
                    style="margin-right: 8px; margin-bottom: 8px"
                    @close="removeTag(tag)"
                  >
                    {{ tag }}
                  </el-tag>
                  <el-input
                    v-if="inputVisible"
                    ref="saveTagInput"
                    v-model="inputValue"
                    size="small"
                    style="width: 100px; margin-right: 8px"
                    @keyup.enter.native="handleInputConfirm"
                    @blur="handleInputConfirm"
                  ></el-input>
                  <el-button v-else size="small" @click="showInput">+ 添加标签</el-button>
                </template>
              </el-form-item>
              <el-form-item label="个人简介">
                <el-input
                  v-model="userInfo.bio"
                  type="textarea"
                  :rows="4"
                  :disabled="!editMode"
                  placeholder="介绍一下你自己吧..."
                  :maxlength="200"
                  show-word-limit
                ></el-input>
              </el-form-item>
              <el-form-item label="个人网站">
                <el-input v-model="userInfo.website" :disabled="!editMode" placeholder="http://">
                  <template slot="prefix">
                    <i class="el-icon-link"></i>
                  </template>
                </el-input>
              </el-form-item>
              <el-form-item v-if="editMode" class="form-actions">
                <el-button type="primary" @click="saveProfile">
                  <i class="el-icon-check"></i> 保存
                </el-button>
                <el-button @click="cancelEdit"> <i class="el-icon-close"></i> 取消 </el-button>
              </el-form-item>
            </el-form>
          </el-card>

          <!-- 技能认证 -->
          <el-card v-if="activeTab === 'skills'" shadow="never" class="content-card">
            <div slot="header" class="card-header">
              <span>技能认证</span>
              <el-button type="primary" size="small" @click="addSkill">
                <i class="el-icon-plus"></i> 添加技能
              </el-button>
            </div>
            <div class="skills-container">
              <div v-for="skill in userInfo.skills" :key="skill.id" class="skill-item">
                <div class="skill-info">
                  <h4>{{ skill.name }}</h4>
                  <p>{{ skill.description }}</p>
                  <el-rate v-model="skill.level" disabled></el-rate>
                </div>
                <div class="skill-actions">
                  <el-tag :type="skill.verified ? 'success' : 'info'">
                    {{ skill.verified ? '已认证' : '未认证' }}
                  </el-tag>
                  <el-dropdown trigger="click">
                    <el-button type="text">
                      <i class="el-icon-more"></i>
                    </el-button>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item @click.native="editSkill(skill)">编辑</el-dropdown-item>
                      <el-dropdown-item v-if="!skill.verified" @click.native="verifySkill(skill)"
                        >申请认证</el-dropdown-item
                      >
                      <el-dropdown-item divided @click.native="deleteSkill(skill)"
                        >删除</el-dropdown-item
                      >
                    </el-dropdown-menu>
                  </el-dropdown>
                </div>
              </div>
            </div>
          </el-card>

          <!-- 活动记录 -->
          <el-card v-if="activeTab === 'activity'" shadow="never" class="content-card">
            <div slot="header" class="card-header">
              <span>活动记录</span>
              <el-date-picker
                v-model="activityDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
                @change="filterActivity"
              >
              </el-date-picker>
            </div>
            <el-timeline>
              <el-timeline-item
                v-for="activity in activities"
                :key="activity.id"
                :timestamp="activity.timestamp"
                :color="activity.color"
              >
                <div class="activity-item">
                  <div class="activity-content">
                    <h4>{{ activity.title }}</h4>
                    <p>{{ activity.description }}</p>
                  </div>
                  <el-tag size="mini" :type="activity.type">{{ activity.category }}</el-tag>
                </div>
              </el-timeline-item>
            </el-timeline>
            <div class="pagination-wrapper">
              <el-pagination
                :current-page="activityPage"
                :page-size="10"
                :total="activityTotal"
                layout="prev, pager, next"
                @current-change="handleActivityPageChange"
              >
              </el-pagination>
            </div>
          </el-card>

          <!-- 安全设置 - 增强版 -->
          <el-card v-if="activeTab === 'security'" shadow="never" class="content-card">
            <div slot="header">
              <span>安全设置</span>
            </div>
            <el-alert
              title="账户安全评分"
              :description="`当前安全评分：${securityScore}/100`"
              type="success"
              show-icon
              :closable="false"
              style="margin-bottom: 20px"
            >
            </el-alert>
            <div class="security-items">
              <div class="security-item">
                <div class="security-info">
                  <div class="security-title">
                    <h4>登录密码</h4>
                    <el-tag size="mini" type="success">已设置</el-tag>
                  </div>
                  <p>定期更改密码可以提高账户安全性，建议3个月更换一次</p>
                  <p class="last-update">上次修改：{{ securityInfo.passwordLastUpdate }}</p>
                </div>
                <el-button type="text" @click="changePassword">修改</el-button>
              </div>
              <el-divider></el-divider>
              <div class="security-item">
                <div class="security-info">
                  <div class="security-title">
                    <h4>手机验证</h4>
                    <el-tag size="mini" :type="userInfo.phone ? 'success' : 'warning'">
                      {{ userInfo.phone ? '已绑定' : '未绑定' }}
                    </el-tag>
                  </div>
                  <p>
                    {{
                      userInfo.phone
                        ? '已绑定手机：' + userInfo.phone
                        : '绑定手机号码用于接收验证码和安全通知'
                    }}
                  </p>
                </div>
                <el-button type="text" @click="bindPhone">{{
                  userInfo.phone ? '更换' : '绑定'
                }}</el-button>
              </div>
              <el-divider></el-divider>
              <div class="security-item">
                <div class="security-info">
                  <div class="security-title">
                    <h4>邮箱验证</h4>
                    <el-tag size="mini" type="success">已验证</el-tag>
                  </div>
                  <p>已验证邮箱：{{ userInfo.email }}</p>
                </div>
                <el-button type="text" @click="changeEmail">更换</el-button>
              </div>
              <el-divider></el-divider>
              <div class="security-item">
                <div class="security-info">
                  <div class="security-title">
                    <h4>二步验证</h4>
                    <el-tag
                      size="mini"
                      :type="securitySettings.twoFactorEnabled ? 'success' : 'info'"
                    >
                      {{ securitySettings.twoFactorEnabled ? '已开启' : '未开启' }}
                    </el-tag>
                  </div>
                  <p>开启后登录需要额外的验证码，大幅提升账户安全性</p>
                </div>
                <el-switch
                  v-model="securitySettings.twoFactorEnabled"
                  @change="toggleTwoFactor"
                ></el-switch>
              </div>
              <el-divider></el-divider>
              <div class="security-item">
                <div class="security-info">
                  <div class="security-title">
                    <h4>安全问题</h4>
                    <el-tag
                      size="mini"
                      :type="securityInfo.securityQuestion ? 'success' : 'warning'"
                    >
                      {{ securityInfo.securityQuestion ? '已设置' : '未设置' }}
                    </el-tag>
                  </div>
                  <p>设置安全问题用于找回密码和身份验证</p>
                </div>
                <el-button type="text" @click="setSecurityQuestion">
                  {{ securityInfo.securityQuestion ? '修改' : '设置' }}
                </el-button>
              </div>
            </div>
          </el-card>

          <!-- 设备管理 -->
          <el-card v-if="activeTab === 'devices'" shadow="never" class="content-card">
            <div slot="header">
              <span>设备管理</span>
            </div>
            <div class="devices-list">
              <div v-for="device in devices" :key="device.id" class="device-item">
                <div class="device-icon">
                  <i :class="getDeviceIcon(device.type)"></i>
                </div>
                <div class="device-info">
                  <h4>{{ device.name }}</h4>
                  <p>{{ device.location }} • {{ device.lastActive }}</p>
                  <p class="device-details">{{ device.browser }} • {{ device.ip }}</p>
                </div>
                <div class="device-status">
                  <el-tag v-if="device.current" type="success" size="small">当前设备</el-tag>
                  <el-tag v-else :type="device.online ? 'success' : 'info'" size="small">
                    {{ device.online ? '在线' : '离线' }}
                  </el-tag>
                </div>
                <div class="device-actions">
                  <el-button
                    v-if="!device.current"
                    type="text"
                    size="small"
                    @click="removeDevice(device)"
                  >
                    移除
                  </el-button>
                </div>
              </div>
            </div>
          </el-card>

          <!-- 登录记录 -->
          <el-card v-if="activeTab === 'loginLog'" shadow="never" class="content-card">
            <div slot="header">
              <span>登录记录</span>
            </div>
            <el-table :data="loginLogs" style="width: 100%">
              <el-table-column prop="time" label="登录时间" width="180"></el-table-column>
              <el-table-column prop="location" label="登录地点" width="150"></el-table-column>
              <el-table-column prop="ip" label="IP地址" width="120"></el-table-column>
              <el-table-column prop="device" label="设备信息"></el-table-column>
              <el-table-column prop="status" label="状态" width="100">
                <template slot-scope="scope">
                  <el-tag
                    :type="scope.row.status === 'success' ? 'success' : 'danger'"
                    size="small"
                  >
                    {{ scope.row.status === 'success' ? '成功' : '失败' }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
            <div class="pagination-wrapper">
              <el-pagination
                :current-page="loginLogPage"
                :page-size="10"
                :total="loginLogTotal"
                layout="prev, pager, next"
                @current-change="handleLoginLogPageChange"
              >
              </el-pagination>
            </div>
          </el-card>

          <!-- 主题设置 -->
          <el-card v-if="activeTab === 'theme'" shadow="never" class="content-card">
            <div slot="header">
              <span>主题设置</span>
            </div>
            <div class="theme-options">
              <h4>选择主题色</h4>
              <div class="color-options">
                <div
                  v-for="color in themeColors"
                  :key="color.name"
                  class="color-option"
                  :class="{ active: themeSettings.primaryColor === color.value }"
                  :style="{ backgroundColor: color.value }"
                  @click="changeThemeColor(color.value)"
                >
                  <i v-if="themeSettings.primaryColor === color.value" class="el-icon-check"></i>
                </div>
              </div>
            </div>
            <el-divider></el-divider>
            <div class="theme-options">
              <h4>界面模式</h4>
              <el-radio-group v-model="themeSettings.mode">
                <el-radio label="light">浅色模式</el-radio>
                <el-radio label="dark">深色模式</el-radio>
                <el-radio label="auto">跟随系统</el-radio>
              </el-radio-group>
            </div>
            <el-divider></el-divider>
            <div class="theme-options">
              <h4>字体大小</h4>
              <el-slider
                v-model="themeSettings.fontSize"
                :min="12"
                :max="18"
                :step="2"
                show-stops
                style="width: 200px"
              ></el-slider>
            </div>
            <div class="save-btn">
              <el-button type="primary" @click="saveThemeSettings">保存设置</el-button>
            </div>
          </el-card>

          <!-- 钱包管理 -->
          <el-card v-if="activeTab === 'wallet'" shadow="never" class="content-card">
            <div slot="header">
              <span>钱包管理</span>
            </div>
            <div class="wallet-overview">
              <el-row :gutter="20">
                <el-col :span="8">
                  <div class="balance-card">
                    <h4>账户余额</h4>
                    <p class="balance-amount">¥{{ userInfo.balance }}</p>
                    <el-button type="primary" size="small">充值</el-button>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="balance-card">
                    <h4>积分余额</h4>
                    <p class="balance-amount">{{ userInfo.points }}</p>
                    <el-button size="small">兑换</el-button>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="balance-card">
                    <h4>优惠券</h4>
                    <p class="balance-amount">{{ userInfo.coupons }}</p>
                    <el-button size="small">查看</el-button>
                  </div>
                </el-col>
              </el-row>
            </div>
            <el-divider></el-divider>
            <div class="wallet-history">
              <h4>交易记录</h4>
              <el-table :data="transactions" style="width: 100%">
                <el-table-column prop="time" label="时间" width="180"></el-table-column>
                <el-table-column prop="type" label="类型" width="100">
                  <template slot-scope="scope">
                    <el-tag
                      :type="scope.row.type === 'income' ? 'success' : 'warning'"
                      size="small"
                    >
                      {{ scope.row.type === 'income' ? '收入' : '支出' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="amount" label="金额" width="120">
                  <template slot-scope="scope">
                    <span :class="scope.row.type === 'income' ? 'income' : 'expense'">
                      {{ scope.row.type === 'income' ? '+' : '-' }}¥{{ scope.row.amount }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="description" label="描述"></el-table-column>
                <el-table-column prop="status" label="状态" width="100">
                  <template slot-scope="scope">
                    <el-tag
                      :type="scope.row.status === 'success' ? 'success' : 'info'"
                      size="small"
                    >
                      {{ scope.row.status === 'success' ? '成功' : '处理中' }}
                    </el-tag>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-card>

          <!-- 数据备份 -->
          <el-card v-if="activeTab === 'backup'" shadow="never" class="content-card">
            <div slot="header">
              <span>数据备份</span>
            </div>
            <div class="backup-options">
              <div class="backup-item">
                <div class="backup-info">
                  <h4>个人资料备份</h4>
                  <p>备份您的个人信息、偏好设置等数据</p>
                  <p class="last-backup">
                    上次备份：{{ backupInfo.profileLastBackup || '从未备份' }}
                  </p>
                </div>
                <el-button type="primary" @click="backupProfile">立即备份</el-button>
              </div>
              <el-divider></el-divider>
              <div class="backup-item">
                <div class="backup-info">
                  <h4>聊天记录备份</h4>
                  <p>备份您的聊天消息和文件记录</p>
                  <p class="last-backup">上次备份：{{ backupInfo.chatLastBackup || '从未备份' }}</p>
                </div>
                <el-button type="primary" @click="backupChat">立即备份</el-button>
              </div>
              <el-divider></el-divider>
              <div class="backup-item">
                <div class="backup-info">
                  <h4>自动备份设置</h4>
                  <p>开启后系统将定期自动备份您的数据</p>
                </div>
                <el-switch
                  v-model="backupSettings.autoBackup"
                  @change="toggleAutoBackup"
                ></el-switch>
              </div>
            </div>
            <el-divider></el-divider>
            <div class="backup-history">
              <h4>备份历史</h4>
              <el-table :data="backupHistory" style="width: 100%">
                <el-table-column prop="time" label="备份时间" width="180"></el-table-column>
                <el-table-column prop="type" label="备份类型" width="120"></el-table-column>
                <el-table-column prop="size" label="文件大小" width="100"></el-table-column>
                <el-table-column prop="status" label="状态" width="100">
                  <template slot-scope="scope">
                    <el-tag
                      :type="scope.row.status === 'success' ? 'success' : 'danger'"
                      size="small"
                    >
                      {{ scope.row.status === 'success' ? '成功' : '失败' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作">
                  <template slot-scope="scope">
                    <el-button type="text" size="small" @click="downloadBackup(scope.row)"
                      >下载</el-button
                    >
                    <el-button type="text" size="small" @click="deleteBackup(scope.row)"
                      >删除</el-button
                    >
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-card>

          <!-- 消息通知 - 增强版 -->
          <el-card v-if="activeTab === 'notification'" shadow="never" class="content-card">
            <div slot="header">
              <span>消息通知</span>
            </div>
            <div class="notification-settings">
              <div class="setting-group">
                <h4>系统通知</h4>
                <div class="notification-item">
                  <div class="notification-info">
                    <h5>安全提醒</h5>
                    <p>账户异常登录、密码修改等安全相关通知</p>
                  </div>
                  <div class="notification-controls">
                    <el-switch v-model="notificationSettings.security.email"></el-switch>
                    <span>邮件</span>
                    <el-switch v-model="notificationSettings.security.sms"></el-switch>
                    <span>短信</span>
                  </div>
                </div>
                <div class="notification-item">
                  <div class="notification-info">
                    <h5>系统更新</h5>
                    <p>新功能发布、系统维护等通知</p>
                  </div>
                  <div class="notification-controls">
                    <el-switch v-model="notificationSettings.system.email"></el-switch>
                    <span>邮件</span>
                    <el-switch v-model="notificationSettings.system.push"></el-switch>
                    <span>推送</span>
                  </div>
                </div>
              </div>
              <el-divider></el-divider>
              <div class="setting-group">
                <h4>社交通知</h4>
                <div class="notification-item">
                  <div class="notification-info">
                    <h5>新消息</h5>
                    <p>收到私信、群消息时的通知</p>
                  </div>
                  <div class="notification-controls">
                    <el-switch v-model="notificationSettings.social.message.push"></el-switch>
                    <span>推送</span>
                    <el-switch v-model="notificationSettings.social.message.sound"></el-switch>
                    <span>声音</span>
                  </div>
                </div>
                <div class="notification-item">
                  <div class="notification-info">
                    <h5>好友动态</h5>
                    <p>好友发布动态、点赞评论等通知</p>
                  </div>
                  <div class="notification-controls">
                    <el-switch v-model="notificationSettings.social.friend.push"></el-switch>
                    <span>推送</span>
                  </div>
                </div>
              </div>
              <el-divider></el-divider>
              <div class="setting-group">
                <h4>免打扰设置</h4>
                <div class="notification-item">
                  <div class="notification-info">
                    <h5>免打扰时间</h5>
                    <p>在指定时间段内不接收通知</p>
                  </div>
                  <div class="notification-controls">
                    <el-time-picker
                      v-model="notificationSettings.dnd.startTime"
                      placeholder="开始时间"
                      style="width: 120px; margin-right: 10px"
                    ></el-time-picker>
                    <span>至</span>
                    <el-time-picker
                      v-model="notificationSettings.dnd.endTime"
                      placeholder="结束时间"
                      style="width: 120px; margin-left: 10px"
                    ></el-time-picker>
                  </div>
                </div>
              </div>
            </div>
            <div class="save-btn">
              <el-button type="primary" @click="saveNotificationSettings">保存设置</el-button>
            </div>
          </el-card>

          <!-- 隐私设置 - 增强版 -->
          <el-card v-if="activeTab === 'privacy'" shadow="never" class="content-card">
            <div slot="header">
              <span>隐私设置</span>
            </div>
            <div class="privacy-settings">
              <div class="privacy-group">
                <h4>个人信息可见性</h4>
                <div class="privacy-item">
                  <div class="privacy-info">
                    <h5>真实姓名</h5>
                    <p>其他用户是否可以看到您的真实姓名</p>
                  </div>
                  <el-select v-model="privacySettings.name" placeholder="选择可见性">
                    <el-option label="所有人" value="public"></el-option>
                    <el-option label="仅好友" value="friends"></el-option>
                    <el-option label="不公开" value="private"></el-option>
                  </el-select>
                </div>
                <div class="privacy-item">
                  <div class="privacy-info">
                    <h5>手机号码</h5>
                    <p>其他用户是否可以通过手机号找到您</p>
                  </div>
                  <el-select v-model="privacySettings.phone" placeholder="选择可见性">
                    <el-option label="所有人" value="public"></el-option>
                    <el-option label="仅好友" value="friends"></el-option>
                    <el-option label="不公开" value="private"></el-option>
                  </el-select>
                </div>
                <div class="privacy-item">
                  <div class="privacy-info">
                    <h5>在线状态</h5>
                    <p>其他用户是否可以看到您的在线状态</p>
                  </div>
                  <el-switch v-model="privacySettings.onlineStatus"></el-switch>
                </div>
              </div>
              <el-divider></el-divider>
              <div class="privacy-group">
                <h4>活动隐私</h4>
                <div class="privacy-item">
                  <div class="privacy-info">
                    <h5>最后活跃时间</h5>
                    <p>其他用户是否可以看到您的最后活跃时间</p>
                  </div>
                  <el-switch v-model="privacySettings.lastSeen"></el-switch>
                </div>
                <div class="privacy-item">
                  <div class="privacy-info">
                    <h5>阅读回执</h5>
                    <p>发送消息阅读状态给对方</p>
                  </div>
                  <el-switch v-model="privacySettings.readReceipts"></el-switch>
                </div>
              </div>
              <el-divider></el-divider>
              <div class="privacy-group">
                <h4>数据使用</h4>
                <div class="privacy-item">
                  <div class="privacy-info">
                    <h5>数据分析</h5>
                    <p>允许系统分析使用数据以改善服务</p>
                  </div>
                  <el-switch v-model="privacySettings.analytics"></el-switch>
                </div>
                <div class="privacy-item">
                  <div class="privacy-info">
                    <h5>个性化推荐</h5>
                    <p>基于使用习惯提供个性化内容推荐</p>
                  </div>
                  <el-switch v-model="privacySettings.personalization"></el-switch>
                </div>
              </div>
            </div>
            <div class="save-btn">
              <el-button type="primary" @click="savePrivacySettings">保存设置</el-button>
            </div>
          </el-card>

          <!-- 账户设置 - 增强版 -->
          <el-card v-if="activeTab === 'account'" shadow="never" class="content-card">
            <div slot="header">
              <span>账户设置</span>
            </div>
            <div class="account-settings">
              <div class="account-item">
                <div class="account-info">
                  <h4>账户状态</h4>
                  <p>当前账户状态：正常</p>
                </div>
                <el-tag type="success">正常</el-tag>
              </div>
              <el-divider></el-divider>
              <div class="account-item">
                <div class="account-info">
                  <h4>注销账户</h4>
                  <p class="danger">注销账户将永久删除您的所有数据，此操作不可撤销</p>
                </div>
                <el-button type="danger" @click="showDeleteAccount">注销账户</el-button>
              </div>
              <el-divider></el-divider>
              <div class="account-item">
                <div class="account-info">
                  <h4>账户冻结</h4>
                  <p>暂时冻结账户，可随时解冻恢复使用</p>
                </div>
                <el-button type="warning" @click="freezeAccount">冻结账户</el-button>
              </div>
              <el-divider></el-divider>
              <div class="account-item">
                <div class="account-info">
                  <h4>数据导出</h4>
                  <p>导出您的所有个人数据</p>
                </div>
                <el-button @click="exportData">导出数据</el-button>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 编辑资料对话框 -->
    <el-dialog title="编辑资料" :visible.sync="editDialogVisible" width="600px">
      <!-- 简化版编辑表单 -->
      <el-form :model="editForm" label-width="80px">
        <el-form-item label="昵称">
          <el-input v-model="editForm.nickname"></el-input>
        </el-form-item>
        <el-form-item label="个人简介">
          <el-input v-model="editForm.bio" type="textarea" :rows="3"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmEdit">确定</el-button>
      </div>
    </el-dialog>

    <!-- 分享名片对话框 -->
    <el-dialog title="我的名片" :visible.sync="qrcodeDialogVisible" width="400px">
      <div class="qrcode-container">
        <div ref="qrcode" class="qrcode"></div>
        <p>扫描二维码查看我的名片</p>
      </div>
    </el-dialog>

    <!-- 修改密码对话框 -->
    <el-dialog title="修改密码" :visible.sync="passwordDialogVisible" width="500px">
      <el-form ref="passwordForm" :model="passwordForm" :rules="passwordRules" label-width="120px">
        <el-form-item label="当前密码" prop="oldPassword">
          <el-input v-model="passwordForm.oldPassword" type="password" show-password></el-input>
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input v-model="passwordForm.newPassword" type="password" show-password></el-input>
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input v-model="passwordForm.confirmPassword" type="password" show-password></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="passwordDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmChangePassword">确定</el-button>
      </div>
    </el-dialog>

    <!-- 技能编辑对话框 -->
    <el-dialog :title="skillDialogTitle" :visible.sync="skillDialogVisible" width="500px">
      <el-form :model="skillForm" label-width="100px">
        <el-form-item label="技能名称">
          <el-input v-model="skillForm.name"></el-input>
        </el-form-item>
        <el-form-item label="技能描述">
          <el-input v-model="skillForm.description" type="textarea" :rows="3"></el-input>
        </el-form-item>
        <el-form-item label="熟练程度">
          <el-rate v-model="skillForm.level"></el-rate>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="skillDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmSkill">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'UserCenter',
  data() {
    return {
      activeTab: 'basicInfo',
      editMode: false,
      editDialogVisible: false,
      qrcodeDialogVisible: false,
      passwordDialogVisible: false,
      skillDialogVisible: false,
      skillDialogTitle: '添加技能',
      inputVisible: false,
      inputValue: '',
      unreadMessages: 5,
      activityDateRange: [],
      activityPage: 1,
      activityTotal: 50,
      loginLogPage: 1,
      loginLogTotal: 100,

      // 用户信息
      userInfo: {
        username: 'john_doe',
        nickname: 'John',
        email: '<EMAIL>',
        phone: '138****1234',
        avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
        status: 'active',
        level: 5,
        vip: true,
        verified: true,
        posts: 128,
        followers: 1234,
        following: 567,
        visits: 5678,
        balance: '1,234.56',
        points: 8900,
        coupons: 12,
        gender: 'male',
        birthday: new Date('1990-01-01'),
        location: ['北京市', '朝阳区'],
        occupation: '软件工程师',
        company: 'ABC科技有限公司',
        tags: ['Vue.js', 'Node.js', '摄影', '旅行'],
        bio: '热爱编程和生活的软件工程师，专注于前端技术，喜欢摄影和旅行。',
        website: 'https://johndoe.com',
        skills: [
          {
            id: 1,
            name: 'JavaScript',
            description: '熟练掌握ES6+语法和异步编程',
            level: 5,
            verified: true,
          },
          {
            id: 2,
            name: 'Vue.js',
            description: '3年Vue开发经验，熟悉生态系统',
            level: 4,
            verified: true,
          },
          {
            id: 3,
            name: 'Node.js',
            description: '后端API开发和服务器部署',
            level: 3,
            verified: false,
          },
        ],
      },

      // 编辑表单
      editForm: {
        nickname: '',
        bio: '',
      },

      // 密码表单
      passwordForm: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: '',
      },

      // 技能表单
      skillForm: {
        name: '',
        description: '',
        level: 0,
      },

      // 城市选项
      cityOptions: [
        {
          value: '北京市',
          label: '北京市',
          children: [
            { value: '朝阳区', label: '朝阳区' },
            { value: '海淀区', label: '海淀区' },
          ],
        },
      ],

      // 活动记录
      activities: [
        {
          id: 1,
          title: '更新了个人资料',
          description: '修改了个人简介和联系方式',
          timestamp: '2023-12-01 14:30',
          color: '#409EFF',
          type: 'primary',
          category: '个人资料',
        },
        {
          id: 2,
          title: '完成了技能认证',
          description: '通过了Vue.js高级认证考试',
          timestamp: '2023-11-28 10:15',
          color: '#67C23A',
          type: 'success',
          category: '技能认证',
        },
      ],

      // 安全信息
      securityScore: 85,
      securityInfo: {
        passwordLastUpdate: '2023-10-15',
        securityQuestion: true,
      },
      securitySettings: {
        twoFactorEnabled: true,
      },

      // 设备列表
      devices: [
        {
          id: 1,
          name: 'MacBook Pro',
          type: 'desktop',
          location: '北京市',
          lastActive: '2小时前',
          browser: 'Chrome 119.0',
          ip: '*************',
          current: true,
          online: true,
        },
        {
          id: 2,
          name: 'iPhone 14',
          type: 'mobile',
          location: '北京市',
          lastActive: '1天前',
          browser: 'Safari',
          ip: '*************',
          current: false,
          online: false,
        },
      ],

      // 登录记录
      loginLogs: [
        {
          time: '2023-12-01 09:30:15',
          location: '北京市',
          ip: '*************',
          device: 'Chrome/MacBook Pro',
          status: 'success',
        },
      ],

      // 主题设置
      themeSettings: {
        primaryColor: '#409EFF',
        mode: 'light',
        fontSize: 14,
      },
      themeColors: [
        { name: '默认蓝', value: '#409EFF' },
        { name: '成功绿', value: '#67C23A' },
        { name: '警告橙', value: '#E6A23C' },
        { name: '危险红', value: '#F56C6C' },
        { name: '信息灰', value: '#909399' },
        { name: '紫色', value: '#9873E8' },
      ],

      // 交易记录
      transactions: [
        {
          id: 1,
          time: '2023-12-01 14:30',
          type: 'income',
          amount: '100.00',
          description: '任务奖励',
          status: 'success',
        },
        {
          id: 2,
          time: '2023-11-30 10:15',
          type: 'expense',
          amount: '50.00',
          description: '购买VIP会员',
          status: 'success',
        },
      ],

      // 备份信息
      backupInfo: {
        profileLastBackup: '2023-11-28',
        chatLastBackup: '2023-11-25',
      },
      backupSettings: {
        autoBackup: true,
      },
      backupHistory: [
        {
          id: 1,
          time: '2023-11-28 10:30',
          type: '个人资料',
          size: '2.5MB',
          status: 'success',
        },
      ],

      // 通知设置
      notificationSettings: {
        security: { email: true, sms: true },
        system: { email: true, push: false },
        social: {
          message: { push: true, sound: true },
          friend: { push: false },
        },
        dnd: {
          startTime: new Date(0, 0, 0, 22, 0, 0),
          endTime: new Date(0, 0, 0, 8, 0, 0),
        },
      },

      // 隐私设置
      privacySettings: {
        name: 'friends',
        phone: 'private',
        onlineStatus: true,
        lastSeen: false,
        readReceipts: true,
        analytics: true,
        personalization: true,
      },

      // 表单验证规则
      rules: {
        username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
        email: [
          { required: true, message: '请输入邮箱', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' },
        ],
      },

      passwordRules: {
        oldPassword: [{ required: true, message: '请输入当前密码', trigger: 'blur' }],
        newPassword: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { min: 6, message: '密码长度不能少于6位', trigger: 'blur' },
        ],
        confirmPassword: [
          { required: true, message: '请确认密码', trigger: 'blur' },
          { validator: this.validatePassword, trigger: 'blur' },
        ],
      },
    };
  },

  methods: {
    handleMenuSelect(index) {
      this.activeTab = index;
    },

    editProfile() {
      this.editMode = true;
      this.editForm.nickname = this.userInfo.nickname;
      this.editForm.bio = this.userInfo.bio;
    },

    saveProfile() {
      this.$refs.userForm.validate((valid) => {
        if (valid) {
          // 保存逻辑
          this.editMode = false;
          this.$message.success('保存成功');
        }
      });
    },

    cancelEdit() {
      this.editMode = false;
    },

    showQRCode() {
      this.qrcodeDialogVisible = true;
      this.$nextTick(() => {
        // 生成二维码的逻辑
        this.$refs.qrcode.innerHTML =
          '<div style="text-align:center;padding:20px;">二维码区域</div>';
      });
    },

    handleQuickAction(action) {
      this.$message.info(`点击了${action}`);
    },

    handleAvatarSuccess(res) {
      this.userInfo.avatar = res.url;
      this.$message.success('头像更新成功');
    },

    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isJPG) {
        this.$message.error('头像图片只能是 JPG/PNG 格式!');
      }
      if (!isLt2M) {
        this.$message.error('头像图片大小不能超过 2MB!');
      }
      return isJPG && isLt2M;
    },

    removeTag(tag) {
      this.userInfo.tags.splice(this.userInfo.tags.indexOf(tag), 1);
    },

    showInput() {
      this.inputVisible = true;
      this.$nextTick(() => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },

    handleInputConfirm() {
      let inputValue = this.inputValue;
      if (inputValue) {
        this.userInfo.tags.push(inputValue);
      }
      this.inputVisible = false;
      this.inputValue = '';
    },

    // 技能相关方法
    addSkill() {
      this.skillDialogTitle = '添加技能';
      this.skillForm = { name: '', description: '', level: 0 };
      this.skillDialogVisible = true;
    },

    editSkill(skill) {
      this.skillDialogTitle = '编辑技能';
      this.skillForm = { ...skill };
      this.skillDialogVisible = true;
    },

    confirmSkill() {
      // 保存技能逻辑
      this.skillDialogVisible = false;
      this.$message.success('技能保存成功');
    },

    deleteSkill(skill) {
      this.$confirm('确定要删除这个技能吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.$message.success('删除成功');
      });
    },

    verifySkill(skill) {
      this.$message.info('认证申请已提交，请等待审核');
    },

    // 安全设置相关方法
    changePassword() {
      this.passwordForm = { oldPassword: '', newPassword: '', confirmPassword: '' };
      this.passwordDialogVisible = true;
    },

    confirmChangePassword() {
      this.$refs.passwordForm.validate((valid) => {
        if (valid) {
          this.passwordDialogVisible = false;
          this.$message.success('密码修改成功');
        }
      });
    },

    validatePassword(rule, value, callback) {
      if (value !== this.passwordForm.newPassword) {
        callback(new Error('两次输入密码不一致!'));
      } else {
        callback();
      }
    },

    toggleTwoFactor(value) {
      this.$message.info(value ? '二步验证已开启' : '二步验证已关闭');
    },

    bindPhone() {
      this.$message.info('手机绑定功能');
    },

    changeEmail() {
      this.$message.info('邮箱更换功能');
    },

    setSecurityQuestion() {
      this.$message.info('安全问题设置功能');
    },

    // 设备管理
    getDeviceIcon(type) {
      const icons = {
        desktop: 'el-icon-monitor',
        mobile: 'el-icon-mobile-phone',
        tablet: 'el-icon-tablet-button',
      };
      return icons[type] || 'el-icon-monitor';
    },

    removeDevice(device) {
      this.$confirm('确定要移除此设备吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.devices = this.devices.filter((d) => d.id !== device.id);
        this.$message.success('设备移除成功');
      });
    },

    // 主题设置
    changeTheme(color) {
      this.themeSettings.primaryColor = color.value;
      // 应用主题色逻辑
      document.documentElement.style.setProperty('--primary-color', color.value);
      this.$message.success(`主题色已更改为${color.name}`);
    },

    changeThemeMode(mode) {
      this.themeSettings.mode = mode;
      // 切换主题模式逻辑
      if (mode === 'dark') {
        document.body.classList.add('dark-theme');
      } else {
        document.body.classList.remove('dark-theme');
      }
      this.$message.success(`已切换为${mode === 'dark' ? '深色' : '浅色'}主题`);
    },

    changeFontSize(size) {
      this.themeSettings.fontSize = size;
      document.documentElement.style.setProperty('--font-size', size + 'px');
      this.$message.success('字体大小已更改');
    },

    resetTheme() {
      this.themeSettings.primaryColor = '#409EFF';
      this.themeSettings.mode = 'light';
      this.themeSettings.fontSize = 14;
      document.documentElement.style.setProperty('--primary-color', '#409EFF');
      document.documentElement.style.setProperty('--font-size', '14px');
      document.body.classList.remove('dark-theme');
      this.$message.success('主题已重置为默认设置');
    },

    // 钱包相关
    recharge() {
      this.$message.info('跳转到充值页面');
    },

    exchangePoints() {
      this.$message.info('跳转到积分兑换页面');
    },

    viewCoupons() {
      this.$message.info('查看优惠券列表');
    },

    // 数据备份
    backupProfile() {
      this.$message.info('开始备份个人资料...');
      // 模拟备份进度
      setTimeout(() => {
        this.$message.success('个人资料备份完成');
        this.backupInfo.profileLastBackup = new Date().toISOString().split('T')[0];
      }, 2000);
    },

    backupChat() {
      this.$message.info('开始备份聊天记录...');
      setTimeout(() => {
        this.$message.success('聊天记录备份完成');
        this.backupInfo.chatLastBackup = new Date().toISOString().split('T')[0];
      }, 3000);
    },

    toggleAutoBackup(value) {
      this.$message.info(value ? '自动备份已开启' : '自动备份已关闭');
    },

    downloadBackup(backup) {
      this.$message.info('开始下载备份文件...');
    },

    deleteBackup(backup) {
      this.$confirm('确定要删除此备份吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.backupHistory = this.backupHistory.filter((b) => b.id !== backup.id);
        this.$message.success('备份删除成功');
      });
    },

    // 通知设置
    saveNotificationSettings() {
      this.$message.success('通知设置已保存');
    },

    // 隐私设置
    savePrivacySettings() {
      this.$message.success('隐私设置已保存');
    },

    // 账户设置
    showDeleteAccount() {
      this.$confirm('确定要注销账户吗？此操作不可撤销，将永久删除您的所有数据。', '危险操作', {
        confirmButtonText: '确定注销',
        cancelButtonText: '取消',
        type: 'error',
        dangerouslyUseHTMLString: true,
        customClass: 'delete-account-confirm',
      }).then(() => {
        this.$message.error('账户注销功能暂未开放');
      });
    },

    freezeAccount() {
      this.$confirm('确定要冻结账户吗？冻结后您将无法登录，但可以随时解冻。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.$message.success('账户冻结功能暂未开放');
      });
    },

    exportData() {
      this.$message.info('正在准备导出数据...');
      setTimeout(() => {
        this.$message.success('数据导出完成，请检查下载目录');
      }, 2000);
    },

    // 活动记录
    handleActivityPageChange(page) {
      this.activityPage = page;
      // 加载对应页面的活动记录
    },

    filterActivities() {
      // 根据日期范围过滤活动记录
      this.$message.info('过滤活动记录');
    },

    // 登录日志
    handleLoginLogPageChange(page) {
      this.loginLogPage = page;
      // 加载对应页面的登录记录
    },

    // 统一的保存方法
    saveSettings(type) {
      this.$message.success(`${type}设置已保存`);
    },

    // 对话框确认方法
    confirmEdit() {
      this.userInfo.nickname = this.editForm.nickname;
      this.userInfo.bio = this.editForm.bio;
      this.editDialogVisible = false;
      this.$message.success('资料更新成功');
    },

    // 获取安全等级颜色
    getSecurityColor(score) {
      if (score >= 80) return '#67C23A';
      if (score >= 60) return '#E6A23C';
      return '#F56C6C';
    },

    // 获取安全等级文字
    getSecurityText(score) {
      if (score >= 80) return '优秀';
      if (score >= 60) return '良好';
      return '需要改进';
    },
  },
};
</script>

<style scoped>
.user-center {
  min-height: 100vh;
  background-color: #f5f7fa;
}

.header-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40px 0;
  color: white;
}

.user-info {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  padding: 0 20px;
}

.avatar-section {
  position: relative;
  margin-right: 30px;
}

.avatar {
  border: 4px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.avatar-uploader {
  position: absolute;
  bottom: 0;
  right: 0;
}

.upload-btn {
  width: 32px;
  height: 32px;
  border: 2px solid white;
  background: #409eff;
}

.level-badge {
  position: absolute;
  top: -10px;
  right: -10px;
}

.info {
  flex: 1;
}

.info h2 {
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 600;
}

.info .email {
  margin: 0 0 15px 0;
  font-size: 16px;
  opacity: 0.9;
}

.status-tags {
  margin-bottom: 20px;
}

.status-tags .el-tag {
  margin-right: 10px;
}

.stats {
  display: flex;
  gap: 30px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.8;
}

.actions {
  margin-left: 30px;
}

.actions .el-button {
  margin-bottom: 10px;
  display: block;
  width: 120px;
}

.quick-actions {
  max-width: 1200px;
  margin: -20px auto 0;
  padding: 0 20px;
  position: relative;
  z-index: 1;
}

.quick-actions .el-card {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.quick-action-item {
  text-align: center;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 8px;
}

.quick-action-item:hover {
  background-color: #f8f9fa;
  transform: translateY(-2px);
}

.quick-action-item i {
  font-size: 32px;
  color: #409eff;
  margin-bottom: 10px;
}

.quick-action-item span {
  display: block;
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.content {
  max-width: 1200px;
  margin: 30px auto;
  padding: 0 20px;
}

.menu-card {
  position: sticky;
  top: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.content-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.user-menu {
  border-right: none;
}

.user-menu .el-menu-item {
  border-radius: 8px;
  margin-bottom: 4px;
  transition: all 0.3s ease;
}

.user-menu .el-menu-item:hover {
  background-color: #f0f9ff;
  color: #409eff;
}

.user-menu .el-menu-item.is-active {
  background-color: #409eff;
  color: white;
}

.user-menu .el-menu-item.is-active:hover {
  background-color: #337ecc;
}

.edit-form {
  padding: 20px;
}

.profile-section {
  margin-bottom: 30px;
}

.profile-section h3 {
  margin-bottom: 20px;
  color: #303133;
  font-size: 18px;
}

.tag-container {
  margin-top: 10px;
}

.tag-container .el-tag {
  margin-right: 10px;
  margin-bottom: 8px;
}

.tag-input {
  width: 90px;
  margin-right: 10px;
  vertical-align: bottom;
}

.skills-section {
  margin-top: 30px;
}

.skill-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  margin-bottom: 10px;
  transition: all 0.3s ease;
}

.skill-item:hover {
  border-color: #409eff;
  background-color: #f8f9fa;
}

.skill-info {
  flex: 1;
}

.skill-name {
  font-weight: 600;
  margin-bottom: 5px;
  display: flex;
  align-items: center;
}

.skill-name .verified-icon {
  color: #67c23a;
  margin-left: 5px;
}

.skill-description {
  color: #909399;
  font-size: 14px;
  margin-bottom: 8px;
}

.skill-level {
  display: flex;
  align-items: center;
  gap: 10px;
}

.skill-actions {
  display: flex;
  gap: 10px;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  margin-right: 15px;
  margin-top: 5px;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-weight: 600;
  margin-bottom: 5px;
  color: #303133;
}

.activity-description {
  color: #909399;
  font-size: 14px;
  margin-bottom: 8px;
}

.activity-meta {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 12px;
  color: #c0c4cc;
}

.security-score {
  text-align: center;
  margin-bottom: 30px;
}

.security-score h3 {
  margin-bottom: 20px;
  color: #303133;
}

.security-item,
.notification-item,
.privacy-item,
.account-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
  border-bottom: 1px solid #f0f0f0;
}

.security-item:last-child,
.notification-item:last-child,
.privacy-item:last-child,
.account-item:last-child {
  border-bottom: none;
}

.security-info,
.notification-info,
.privacy-info,
.account-info {
  flex: 1;
}

.security-info h4,
.notification-info h4,
.privacy-info h4,
.account-info h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 16px;
}

.security-info p,
.notification-info p,
.privacy-info p,
.account-info p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.device-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  margin-bottom: 10px;
}

.device-icon {
  margin-right: 15px;
  font-size: 24px;
  color: #409eff;
}

.device-info {
  flex: 1;
}

.device-name {
  font-weight: 600;
  margin-bottom: 5px;
  display: flex;
  align-items: center;
}

.device-name .current-device {
  margin-left: 10px;
}

.device-details {
  color: #909399;
  font-size: 14px;
}

.device-actions {
  display: flex;
  gap: 10px;
}

.theme-colors {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.color-item {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  border: 3px solid transparent;
  transition: all 0.3s ease;
}

.color-item:hover {
  transform: scale(1.1);
}

.color-item.active {
  border-color: #303133;
}

.balance-card {
  text-align: center;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
}

.balance-card h4 {
  margin: 0 0 10px 0;
  font-size: 16px;
  opacity: 0.9;
}

.balance-amount {
  font-size: 24px;
  font-weight: 600;
  margin: 10px 0 15px 0;
}

.wallet-history {
  margin-top: 30px;
}

.income {
  color: #67c23a;
  font-weight: 600;
}

.expense {
  color: #f56c6c;
  font-weight: 600;
}

.backup-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
}

.backup-info {
  flex: 1;
}

.backup-info h4 {
  margin: 0 0 8px 0;
  color: #303133;
}

.backup-info p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.last-backup {
  color: #409eff !important;
  font-size: 12px !important;
}

.notification-settings {
  margin-bottom: 30px;
}

.setting-group {
  margin-bottom: 30px;
}

.setting-group h4 {
  margin-bottom: 20px;
  color: #303133;
  font-size: 16px;
}

.notification-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.notification-controls span {
  font-size: 14px;
  color: #606266;
}

.privacy-settings {
  margin-bottom: 30px;
}

.privacy-group {
  margin-bottom: 30px;
}

.privacy-group h4 {
  margin-bottom: 20px;
  color: #303133;
  font-size: 16px;
}

.danger .account-info h4 {
  color: #f56c6c;
}

.danger .account-info p {
  color: #f56c6c;
}

.save-btn {
  margin-top: 30px;
  text-align: right;
}

.dialog-footer {
  text-align: right;
}

.qrcode-container {
  text-align: center;
  padding: 20px;
}

.qrcode {
  width: 200px;
  height: 200px;
  margin: 0 auto 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .content {
    padding: 0 15px;
  }

  .user-info {
    padding: 0 15px;
  }
}

@media (max-width: 768px) {
  .user-info {
    flex-direction: column;
    text-align: center;
  }

  .avatar-section {
    margin-right: 0;
    margin-bottom: 20px;
  }

  .actions {
    margin-left: 0;
    margin-top: 20px;
  }

  .actions .el-button {
    display: inline-block;
    width: auto;
    margin-right: 10px;
  }

  .stats {
    justify-content: center;
  }

  .content .el-row {
    flex-direction: column;
  }

  .content .el-col {
    width: 100%;
    margin-bottom: 20px;
  }

  .quick-actions .el-row {
    flex-wrap: wrap;
  }

  .quick-action-item {
    padding: 15px;
  }

  .quick-action-item i {
    font-size: 24px;
  }
}

@media (max-width: 480px) {
  .header-bg {
    padding: 20px 0;
  }

  .info h2 {
    font-size: 24px;
  }

  .stats {
    gap: 20px;
  }

  .stat-number {
    font-size: 18px;
  }

  .balance-card {
    padding: 15px;
  }

  .balance-amount {
    font-size: 20px;
  }
}

/* 深色主题 */
.dark-theme {
  background-color: #1a1a1a;
  color: #ffffff;
}

.dark-theme .user-center {
  background-color: #1a1a1a;
}

.dark-theme .el-card {
  background-color: #2d2d2d;
  border-color: #404040;
}

.dark-theme .content-card {
  background-color: #2d2d2d;
  border-color: #404040;
}

.dark-theme .skill-item {
  background-color: #2d2d2d;
  border-color: #404040;
}

.dark-theme .device-item {
  background-color: #2d2d2d;
  border-color: #404040;
}

/* 删除账户确认对话框样式 */
.delete-account-confirm .el-message-box {
  border: 2px solid #f56c6c;
}

.delete-account-confirm .el-message-box__title {
  color: #f56c6c;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.content-card {
  animation: fadeIn 0.3s ease;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 加载动画 */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid #409eff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>
